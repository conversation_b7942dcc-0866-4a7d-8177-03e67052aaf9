#ifndef AMSSDK_SERVICE_MANAGER_H
#define AMSSDK_SERVICE_MANAGER_H

#include <memory>
#include <string>
#include "../api/api_manager.h"

namespace amssdk {

// 简化的服务管理器，替代原来的AmsClient
// 只负责Service的创建和配置管理，不再有转发函数
class ServiceManager {
 public:
  LIBAMS_EXPORT explicit ServiceManager(const std::string& base_url);
  LIBAMS_EXPORT ~ServiceManager() = default;

  // 配置方法
  LIBAMS_EXPORT bool SetAuthorizationKey(const std::string& key) const;
  LIBAMS_EXPORT void SetMaxTimeout(int32_t ms) const;
  LIBAMS_EXPORT void SetBaseUrl(const std::string& base_url) const;

  // 直接获取Service实例
  LIBAMS_EXPORT ChatService& GetChatService() const;
  LIBAMS_EXPORT ConversationService& GetConversationService() const;
  LIBAMS_EXPORT WorkflowService& GetWorkflowService() const;
  LIBAMS_EXPORT KnowledgeService& GetKnowledgeService() const;
  LIBAMS_EXPORT FileService& GetFileService() const;
  LIBAMS_EXPORT AudioService& GetAudioService() const;
  LIBAMS_EXPORT AppService& GetAppService() const;
  LIBAMS_EXPORT TaskService& GetTaskService() const;

 private:
  std::unique_ptr<ApiManager> api_manager_;
};

}  // namespace amssdk

#endif  // AMSSDK_SERVICE_MANAGER_H
