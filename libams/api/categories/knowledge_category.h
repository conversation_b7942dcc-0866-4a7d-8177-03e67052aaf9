#ifndef AMSSDK_KNOWLEDGE_CATEGORY_H
#define AMSSDK_KNOWLEDGE_CATEGORY_H

#include "category_base.h"
#include "include/common/api_result.h"

namespace amssdk {

// Knowledge分类 - 包含知识库相关的API
class KnowledgeCategory : public CategoryBase {
 public:
  LIBAMS_EXPORT
  explicit KnowledgeCategory(AmsClient& ams_client);

  // Dataset Management
  LIBAMS_EXPORT
  ApiResult<CreateDatasetResponse> CreateDataset(
      const CreateDatasetRequest& request) const;

  LIBAMS_EXPORT
  ApiResult<ListDatasetsResponse> ListDatasets(
      const ListDatasetsRequest& request) const;

  LIBAMS_EXPORT
  ApiResult<SimpleResponse> DeleteDataset(
      const DeleteDatasetRequest& request) const;

  // Document Management
  LIBAMS_EXPORT
  ApiResult<CreateDocumentResponse> CreateDocumentByText(
      const CreateDocumentByTextRequest& request) const;

  LIBAMS_EXPORT
  ApiResult<CreateDocumentResponse> CreateDocumentByFile(
      const CreateDocumentByFileRequest& request) const;

  LIBAMS_EXPORT
  ApiResult<CreateDocumentResponse> UpdateDocumentByText(
      const UpdateDocumentByTextRequest& request) const;

  LIBAMS_EXPORT
  ApiResult<CreateDocumentResponse> UpdateDocumentByFile(
      const UpdateDocumentByFileRequest& request) const;

  LIBAMS_EXPORT
  ApiResult<GetIndexingStatusResponse> GetIndexingStatus(
      const GetIndexingStatusRequest& request) const;

  LIBAMS_EXPORT
  ApiResult<DeleteDocumentResponse> DeleteDocument(
      const DeleteDocumentRequest& request) const;

  LIBAMS_EXPORT
  ApiResult<ListDocumentsResponse> ListDocuments(
      const ListDocumentsRequest& request) const;

  // Segment Management
  LIBAMS_EXPORT
  ApiResult<CreateSegmentResponse> CreateSegment(
      const CreateSegmentRequest& request) const;

  LIBAMS_EXPORT
  ApiResult<ListSegmentsResponse> ListSegments(
      const ListSegmentsRequest& request) const;

  LIBAMS_EXPORT
  ApiResult<DeleteSegmentResponse> DeleteSegment(
      const DeleteSegmentRequest& request) const;

  LIBAMS_EXPORT
  ApiResult<UpdateSegmentResponse> UpdateSegment(
      const UpdateSegmentRequest& request) const;

  // Retrieval
  LIBAMS_EXPORT
  ApiResult<RetrieveDatasetResponse> RetrieveDataset(
      const RetrieveDatasetRequest& request) const;
};

} // namespace amssdk

#endif // AMSSDK_KNOWLEDGE_CATEGORY_H
