#ifndef AMSSDK_CHAT_CATEGORY_H
#define AMSSDK_CHAT_CATEGORY_H

#include "category_base.h"
#include "include/common/api_result.h"

namespace amssdk {

// Chat分类 - 包含聊天相关的API
class ChatCategory : public CategoryBase {
 public:
  LIBAMS_EXPORT
  explicit ChatCategory(AmsClient& ams_client);

  // 发送聊天消息
  LIBAMS_EXPORT
  ApiResult<void> SendChatMessage(
      const ChatRequest& request,
      const AmsClient::StreamEventCallback& callback) const;

  // 发送补全消息
  LIBAMS_EXPORT
  ApiResult<void> SendCompletionMessage(
      const CompletionMessageRequest& request,
      const AmsClient::StreamEventCallback& callback) const;

  // 获取建议回复
  LIBAMS_EXPORT
  ApiResult<SuggestedResponse> GetSuggested(
      const SuggestedRequest& request) const;

  // 获取消息历史
  LIBAMS_EXPORT
  ApiResult<MessagesResponse> GetMessages(
      const MessagesRequest& request) const;

  // 发送反馈
  LIBAMS_EXPORT
  ApiResult<SimpleResponse> SendFeedback(
      const FeedbackRequest& request) const;
};

} // namespace amssdk

#endif // AMSSDK_CHAT_CATEGORY_H
