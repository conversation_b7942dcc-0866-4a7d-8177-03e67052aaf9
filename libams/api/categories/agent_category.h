#ifndef AMSSDK_AGENT_CATEGORY_H
#define AMSSDK_AGENT_CATEGORY_H

#include "category_base.h"
#include "include/common/api_result.h"

namespace amssdk {

// Agent分类 - 包含所有API，因为Agent可能需要使用所有功能
class AgentCategory : public CategoryBase {
 public:
  LIBAMS_EXPORT
  explicit AgentCategory(AmsClient& ams_client);

  // Chat相关API
  LIBAMS_EXPORT
  ApiResult<void> SendChatMessage(
      const ChatRequest& request, const AmsClient::StreamEventCallback& callback) const;

  LIBAMS_EXPORT
  ApiResult<void> SendCompletionMessage(
      const CompletionMessageRequest& request,
      const AmsClient::StreamEventCallback& callback) const;

  LIBAMS_EXPORT
  ApiResult<SuggestedResponse> GetSuggested(
      const SuggestedRequest& request) const;

  LIBAMS_EXPORT
  ApiResult<MessagesResponse> GetMessages(
      const MessagesRequest& request) const;

  // Conversation相关API
  LIBAMS_EXPORT
  ApiResult<ConversationResponse> GetConversation(
      const ConversationRequest& request) const;

  LIBAMS_EXPORT
  ApiResult<SimpleResponse> DeleteConversation(
      const DeleteConversationRequest& request) const;

  LIBAMS_EXPORT
  ApiResult<RenameConversationResponse> RenameConversation(
      const RenameConversationRequest& request) const;

  // File相关API
  LIBAMS_EXPORT
  ApiResult<FileResponse> FileUpload(
      const FileRequest& request) const;

  // Task相关API
  LIBAMS_EXPORT
  ApiResult<SimpleResponse> StopTask(
      const TaskStopRequest& request) const;

  LIBAMS_EXPORT
  ApiResult<SimpleResponse> SendFeedback(
      const FeedbackRequest& request) const;

  // Audio相关API
  LIBAMS_EXPORT
  ApiResult<AudioToTextResponse> AudioToText(
      const AudioToTextRequest& request) const;

  // App相关API
  LIBAMS_EXPORT
  ApiResult<AppMetaResponse> AppMeta(
      const AppMetaRequest& request) const;

  LIBAMS_EXPORT
  ApiResult<AppInfoResponse> AppInfo(
      const AppInfoRequest& request) const;

  LIBAMS_EXPORT
  ApiResult<AppParamResponse> AppParameters(
      const AppParamRequest& request) const;

  // Workflow相关API
  LIBAMS_EXPORT
  ApiResult<WorkflowRunResponse> WorkflowRun(
      const WorkflowRunRequest& request,
      const AmsClient::StreamEventCallback& stream_event_callback) const;

  LIBAMS_EXPORT
  ApiResult<WorkflowRunInfoResponse> WorkflowRunInfo(
      const WorkflowRunInfoRequest& request) const;

  LIBAMS_EXPORT
  ApiResult<SimpleResponse> WorkflowTaskStop(
      const WorkflowTaskStopRequest& request) const;

  LIBAMS_EXPORT
  ApiResult<WorkflowLogsResponse> WorkflowLogs(
      const WorkflowLogsRequest& request) const;

  // Knowledge相关API - Dataset Management
  LIBAMS_EXPORT
  ApiResult<CreateDatasetResponse> CreateDataset(
      const CreateDatasetRequest& request) const;

  LIBAMS_EXPORT
  ApiResult<ListDatasetsResponse> ListDatasets(
      const ListDatasetsRequest& request) const;

  LIBAMS_EXPORT
  ApiResult<SimpleResponse> DeleteDataset(
      const DeleteDatasetRequest& request) const;

  // Knowledge相关API - Document Management
  LIBAMS_EXPORT
  ApiResult<CreateDocumentResponse> CreateDocumentByText(
      const CreateDocumentByTextRequest& request) const;

  LIBAMS_EXPORT
  ApiResult<CreateDocumentResponse> CreateDocumentByFile(
      const CreateDocumentByFileRequest& request) const;

  LIBAMS_EXPORT
  ApiResult<CreateDocumentResponse> UpdateDocumentByText(
      const UpdateDocumentByTextRequest& request) const;

  LIBAMS_EXPORT
  ApiResult<CreateDocumentResponse> UpdateDocumentByFile(
      const UpdateDocumentByFileRequest& request) const;

  LIBAMS_EXPORT
  ApiResult<GetIndexingStatusResponse> GetIndexingStatus(
      const GetIndexingStatusRequest& request) const;

  LIBAMS_EXPORT
  ApiResult<DeleteDocumentResponse> DeleteDocument(
      const DeleteDocumentRequest& request) const;

  LIBAMS_EXPORT
  ApiResult<ListDocumentsResponse> ListDocuments(
      const ListDocumentsRequest& request) const;

  // Knowledge相关API - Segment Management
  LIBAMS_EXPORT
  ApiResult<CreateSegmentResponse> CreateSegment(
      const CreateSegmentRequest& request) const;

  LIBAMS_EXPORT
  ApiResult<ListSegmentsResponse> ListSegments(
      const ListSegmentsRequest& request) const;

  LIBAMS_EXPORT
  ApiResult<DeleteSegmentResponse> DeleteSegment(
      const DeleteSegmentRequest& request) const;

  LIBAMS_EXPORT
  ApiResult<UpdateSegmentResponse> UpdateSegment(
      const UpdateSegmentRequest& request) const;

  // Knowledge相关API - Retrieval
  LIBAMS_EXPORT
  ApiResult<RetrieveDatasetResponse> RetrieveDataset(
      const RetrieveDatasetRequest& request) const;
};

} // namespace amssdk

#endif // AMSSDK_AGENT_CATEGORY_H
