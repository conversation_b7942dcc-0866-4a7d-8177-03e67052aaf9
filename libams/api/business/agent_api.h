#ifndef AMSSDK_AGENT_API_H
#define AMSSDK_AGENT_API_H

#include <functional>
#include <map>
#include <memory>
#include <string>

#include "api/services/app_service.h"
#include "api/services/chat_service.h"
#include "api/services/conversation_service.h"
#include "api/services/knowledge_service.h"

namespace amssdk {

class ApiManager;

class AgentAPI {
 public:
  using StreamEventCallback = std::function<void(std::unique_ptr<StreamEvent>)>;

  LIBAMS_EXPORT explicit AgentAPI(ApiManager& api_manager);
  LIBAMS_EXPORT ~AgentAPI() = default;

  // Agent特定的高级业务接口
  LIBAMS_EXPORT ApiResult<void> StartConversation(
      const std::string& agent_id, const std::string& initial_message,
      const StreamEventCallback& callback) const;

  LIBAMS_EXPORT ApiResult<void> SendMessage(
      const std::string& conversation_id, const std::string& message,
      const StreamEventCallback& callback) const;

  LIBAMS_EXPORT ApiResult<ConversationResponse> GetConversation(
      const std::string& conversation_id) const;

  LIBAMS_EXPORT ApiResult<void> AddKnowledge(
      const std::string& agent_id, const std::string& dataset_id) const;

  LIBAMS_EXPORT ApiResult<AppInfoResponse> GetAgentInfo(
      const std::string& agent_id) const;

  // Agent配置相关
  LIBAMS_EXPORT ApiResult<AppParamResponse> GetAgentParameters(
      const std::string& agent_id) const;

  LIBAMS_EXPORT ApiResult<void> UpdateAgentParameters(
      const std::string& agent_id,
      const std::map<std::string, std::string>& parameters) const;

 private:
  ApiManager& api_manager_;  // 通过ApiManager访问所有Service和配置
};

}  // namespace amssdk

#endif  // AMSSDK_AGENT_API_H
