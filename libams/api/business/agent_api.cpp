#include "agent_api.h"
#include "../api_manager.h"
#include "../../include/chat/chat_request.h"
#include "../../include/conversation/conversation_request.h"
#include "../../include/app.h"

namespace amssdk {

AgentAPI::AgentAPI(ApiManager& api_manager) : api_manager_(api_manager) {}

ApiResult<void> AgentAPI::StartConversation(
    const std::string& agent_id,
    const std::string& initial_message,
    const StreamEventCallback& callback) const {
  
  // 构建ChatRequest
  ChatRequest request;
  // 设置agent_id和initial_message到request中
  // request.SetAgentId(agent_id);
  // request.SetMessage(initial_message);
  
  // 通过ApiManager获取ChatService并调用
  return api_manager_.chat().SendChatMessage(request, callback);
}

ApiResult<void> AgentAPI::SendMessage(
    const std::string& conversation_id,
    const std::string& message,
    const StreamEventCallback& callback) const {
  
  // 构建ChatRequest
  ChatRequest request;
  // 设置conversation_id和message到request中
  // request.SetConversationId(conversation_id);
  // request.SetMessage(message);
  
  return api_manager_.chat().SendChatMessage(request, callback);
}

ApiResult<ConversationResponse> AgentAPI::GetConversation(
    const std::string& conversation_id) const {
  
  ConversationRequest request;
  // request.SetConversationId(conversation_id);
  
  return api_manager_.conversation().GetConversation(request);
}

ApiResult<void> AgentAPI::AddKnowledge(
    const std::string& agent_id,
    const std::string& dataset_id) const {
  
  // 这里可能需要调用特定的API来关联Agent和知识库
  // 具体实现取决于后端API设计
  
  // 示例：可能需要更新Agent的配置
  // AppParamRequest request;
  // request.SetAgentId(agent_id);
  // request.AddParameter("knowledge_dataset", dataset_id);
  // return api_manager_.app().UpdateParameters(request);
  
  // 临时返回成功，实际实现需要根据具体API
  return ApiResult<void>::Success();
}

ApiResult<AppInfoResponse> AgentAPI::GetAgentInfo(
    const std::string& agent_id) const {
  
  AppInfoRequest request;
  // request.SetAppId(agent_id);
  
  return api_manager_.app().Info(request);
}

ApiResult<AppParamResponse> AgentAPI::GetAgentParameters(
    const std::string& agent_id) const {
  
  AppParamRequest request;
  // request.SetAppId(agent_id);
  
  return api_manager_.app().Parameters(request);
}

ApiResult<void> AgentAPI::UpdateAgentParameters(
    const std::string& agent_id,
    const std::map<std::string, std::string>& parameters) const {
  
  // 这里需要根据实际的API设计来实现参数更新
  // 可能需要调用特定的更新API
  
  // 临时返回成功，实际实现需要根据具体API
  return ApiResult<void>::Success();
}

}  // namespace amssdk
