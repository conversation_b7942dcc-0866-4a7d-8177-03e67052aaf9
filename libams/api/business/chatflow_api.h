#ifndef AMSSDK_CHATFLOW_API_H
#define AMSSDK_CHATFLOW_API_H

#include <functional>
#include <memory>
#include <string>
#include <map>

#include "../services/workflow_service.h"
#include "../services/chat_service.h"
#include "../services/conversation_service.h"

namespace amssdk {

class ApiManager;

class ChatflowAPI {
 public:
  using StreamEventCallback = std::function<void(std::unique_ptr<StreamEvent>)>;

  LIBAMS_EXPORT explicit ChatflowAPI(ApiManager& api_manager);
  LIBAMS_EXPORT ~ChatflowAPI() = default;

  // Chatflow特定的高级业务接口
  LIBAMS_EXPORT ApiResult<WorkflowRunResponse> Run(
      const std::string& workflow_id,
      const std::map<std::string, std::string>& inputs,
      const StreamEventCallback& callback) const;

  LIBAMS_EXPORT ApiResult<WorkflowRunInfoResponse> GetStatus(
      const std::string& run_id) const;

  LIBAMS_EXPORT ApiResult<void> Interact(
      const std::string& run_id,
      const std::string& message,
      const StreamEventCallback& callback) const;

  LIBAMS_EXPORT ApiResult<SimpleResponse> Stop(
      const std::string& run_id) const;

  LIBAMS_EXPORT ApiResult<WorkflowLogsResponse> GetLogs(
      const std::string& run_id) const;

  // 批量操作
  LIBAMS_EXPORT ApiResult<void> RunBatch(
      const std::string& workflow_id,
      const std::vector<std::map<std::string, std::string>>& batch_inputs,
      const StreamEventCallback& callback) const;

 private:
  WorkflowService& workflow_service_;
  ChatService& chat_service_;
  ConversationService& conversation_service_;
};

}  // namespace amssdk

#endif  // AMSSDK_CHATFLOW_API_H
