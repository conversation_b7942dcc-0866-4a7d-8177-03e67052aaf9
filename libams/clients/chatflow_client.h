#ifndef AMSSDK_CHATFLOW_CLIENT_H
#define AMSSDK_CHATFLOW_CLIENT_H

#include "../api/api_manager.h"
#include "../api/services/chat_service.h"
#include "../api/services/workflow_service.h"
#include "../api/services/conversation_service.h"
#include "../api/services/file_service.h"

namespace amssdk {

class ChatflowClient {
 public:
  using StreamEventCallback = std::function<void(std::unique_ptr<StreamEvent>)>;

  LIBAMS_EXPORT explicit ChatflowClient(const std::string& base_url);
  LIBAMS_EXPORT ~ChatflowClient() = default;

  // 认证和配置
  LIBAMS_EXPORT bool SetAuthorizationKey(const std::string& key) const;
  LIBAMS_EXPORT void SetMaxTimeout(int32_t ms) const;

  // Chatflow特定的高级接口
  LIBAMS_EXPORT ApiResult<void> StartChatflow(
      const std::string& workflow_id,
      const std::map<std::string, std::string>& inputs,
      const StreamEventCallback& callback) const;

  LIBAMS_EXPORT ApiResult<WorkflowRunInfoResponse> GetChatflowStatus(
      const std::string& run_id) const;

  LIBAMS_EXPORT ApiResult<void> InteractWithChatflow(
      const std::string& run_id,
      const std::string& message,
      const StreamEventCallback& callback) const;

  LIBAMS_EXPORT ApiResult<SimpleResponse> StopChatflow(
      const std::string& run_id) const;

  // 直接访问Service（推荐方式）
  LIBAMS_EXPORT ChatService& GetChatService() const;
  LIBAMS_EXPORT WorkflowService& GetWorkflowService() const;
  LIBAMS_EXPORT ConversationService& GetConversationService() const;
  LIBAMS_EXPORT FileService& GetFileService() const;

 private:
  std::unique_ptr<ApiManager> api_manager_;
};

}  // namespace amssdk

#endif  // AMSSDK_CHATFLOW_CLIENT_H
