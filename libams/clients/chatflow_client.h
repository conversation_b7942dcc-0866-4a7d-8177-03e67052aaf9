#ifndef AMSSDK_CHATFLOW_CLIENT_H
#define AMSSDK_CHATFLOW_CLIENT_H

#include "../modules/chat_module.h"
#include "../modules/workflow_module.h"
#include "../modules/conversation_module.h"
#include "../modules/file_module.h"

namespace amssdk {

class ChatflowClient {
 public:
  using StreamEventCallback = std::function<void(std::unique_ptr<StreamEvent>)>;

  LIBAMS_EXPORT explicit ChatflowClient(const std::string& base_url);
  LIBAMS_EXPORT ~ChatflowClient() = default;

  // 认证和配置
  LIBAMS_EXPORT bool SetAuthorizationKey(const std::string& key) const;
  LIBAMS_EXPORT void SetMaxTimeout(int32_t ms) const;

  // Chatflow特定的高级接口
  LIBAMS_EXPORT ApiResult<void> StartChatflow(
      const std::string& workflow_id,
      const std::map<std::string, std::string>& inputs,
      const StreamEventCallback& callback) const;

  LIBAMS_EXPORT ApiResult<WorkflowRunInfoResponse> GetChatflowStatus(
      const std::string& run_id) const;

  LIBAMS_EXPORT ApiResult<void> InteractWithChatflow(
      const std::string& run_id,
      const std::string& message,
      const StreamEventCallback& callback) const;

  LIBAMS_EXPORT ApiResult<SimpleResponse> StopChatflow(
      const std::string& run_id) const;

  // 直接访问模块（高级用户）
  LIBAMS_EXPORT ChatModule& GetChatModule() const { return *chat_module_; }
  LIBAMS_EXPORT WorkflowModule& GetWorkflowModule() const { return *workflow_module_; }
  LIBAMS_EXPORT ConversationModule& GetConversationModule() const { return *conversation_module_; }

 private:
  std::shared_ptr<AmsClient> core_client_;
  std::unique_ptr<ChatModule> chat_module_;
  std::unique_ptr<WorkflowModule> workflow_module_;
  std::unique_ptr<ConversationModule> conversation_module_;
  std::unique_ptr<FileModule> file_module_;
};

}  // namespace amssdk

#endif  // AMSSDK_CHATFLOW_CLIENT_H
