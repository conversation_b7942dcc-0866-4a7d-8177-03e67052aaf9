#ifndef AMSSDK_AGENT_CLIENT_H
#define AMSSDK_AGENT_CLIENT_H

#include "../api/api_manager.h"
#include "../api/services/chat_service.h"
#include "../api/services/conversation_service.h"
#include "../api/services/knowledge_service.h"
#include "../api/services/file_service.h"
#include "../api/services/app_service.h"

namespace amssdk {

class AgentClient {
 public:
  using StreamEventCallback = std::function<void(std::unique_ptr<StreamEvent>)>;

  LIBAMS_EXPORT explicit AgentClient(const std::string& base_url);
  LIBAMS_EXPORT ~AgentClient() = default;

  // 认证和配置
  LIBAMS_EXPORT bool SetAuthorizationKey(const std::string& key) const;
  LIBAMS_EXPORT void SetMaxTimeout(int32_t ms) const;

  // Agent特定的高级接口
  LIBAMS_EXPORT ApiResult<void> StartAgentConversation(
      const std::string& agent_id,
      const std::string& initial_message,
      const StreamEventCallback& callback) const;

  LIBAMS_EXPORT ApiResult<void> SendMessageToAgent(
      const std::string& conversation_id,
      const std::string& message,
      const StreamEventCallback& callback) const;

  LIBAMS_EXPORT ApiResult<ConversationResponse> GetAgentConversation(
      const std::string& conversation_id) const;

  // 知识库集成
  LIBAMS_EXPORT ApiResult<void> AddKnowledgeToAgent(
      const std::string& agent_id,
      const std::string& dataset_id) const;

  // 直接访问Service（推荐方式）
  LIBAMS_EXPORT ChatService& GetChatService() const;
  LIBAMS_EXPORT ConversationService& GetConversationService() const;
  LIBAMS_EXPORT KnowledgeService& GetKnowledgeService() const;
  LIBAMS_EXPORT FileService& GetFileService() const;
  LIBAMS_EXPORT AppService& GetAppService() const;

 private:
  std::unique_ptr<ApiManager> api_manager_;
};

}  // namespace amssdk

#endif  // AMSSDK_AGENT_CLIENT_H
