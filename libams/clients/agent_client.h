#ifndef AMSSDK_AGENT_CLIENT_H
#define AMSSDK_AGENT_CLIENT_H

#include "../modules/chat_module.h"
#include "../modules/conversation_module.h"
#include "../modules/knowledge_module.h"
#include "../modules/file_module.h"
#include "../modules/app_module.h"

namespace amssdk {

class AgentClient {
 public:
  using StreamEventCallback = std::function<void(std::unique_ptr<StreamEvent>)>;

  LIBAMS_EXPORT explicit AgentClient(const std::string& base_url);
  LIBAMS_EXPORT ~AgentClient() = default;

  // 认证和配置
  LIBAMS_EXPORT bool SetAuthorizationKey(const std::string& key) const;
  LIBAMS_EXPORT void SetMaxTimeout(int32_t ms) const;

  // Agent特定的高级接口
  LIBAMS_EXPORT ApiResult<void> StartAgentConversation(
      const std::string& agent_id,
      const std::string& initial_message,
      const StreamEventCallback& callback) const;

  LIBAMS_EXPORT ApiResult<void> SendMessageToAgent(
      const std::string& conversation_id,
      const std::string& message,
      const StreamEventCallback& callback) const;

  LIBAMS_EXPORT ApiResult<ConversationResponse> GetAgentConversation(
      const std::string& conversation_id) const;

  // 知识库集成
  LIBAMS_EXPORT ApiResult<void> AddKnowledgeToAgent(
      const std::string& agent_id,
      const std::string& dataset_id) const;

  // 直接访问模块（高级用户）
  LIBAMS_EXPORT ChatModule& GetChatModule() const { return *chat_module_; }
  LIBAMS_EXPORT ConversationModule& GetConversationModule() const { return *conversation_module_; }
  LIBAMS_EXPORT KnowledgeModule& GetKnowledgeModule() const { return *knowledge_module_; }

 private:
  std::shared_ptr<AmsClient> core_client_;
  std::unique_ptr<ChatModule> chat_module_;
  std::unique_ptr<ConversationModule> conversation_module_;
  std::unique_ptr<KnowledgeModule> knowledge_module_;
  std::unique_ptr<FileModule> file_module_;
  std::unique_ptr<AppModule> app_module_;
};

}  // namespace amssdk

#endif  // AMSSDK_AGENT_CLIENT_H
