#ifndef AMSSDK_CHAT_MODULE_H
#define AMSSDK_CHAT_MODULE_H

#include "../core/ams_client.h"
#include "../include/chat/chat_request.h"
#include "../include/chat/chat_stream_event.h"
#include "../include/common/api_result.h"

namespace amssdk {

class ChatModule {
 public:
  using StreamEventCallback = std::function<void(std::unique_ptr<StreamEvent>)>;

  LIBAMS_EXPORT explicit ChatModule(std::shared_ptr<AmsClient> client);
  LIBAMS_EXPORT ~ChatModule() = default;

  // 聊天相关API
  LIBAMS_EXPORT ApiResult<void> SendChatMessage(
      const ChatRequest& request, const StreamEventCallback& callback) const;
  
  LIBAMS_EXPORT ApiResult<void> SendCompletionMessage(
      const CompletionMessageRequest& request,
      const StreamEventCallback& callback) const;
  
  LIBAMS_EXPORT ApiResult<MessagesResponse> GetMessages(
      const MessagesRequest& request) const;
  
  LIBAMS_EXPORT ApiResult<SuggestedResponse> GetSuggested(
      const SuggestedRequest& request) const;
  
  LIBAMS_EXPORT ApiResult<SimpleResponse> SendFeedback(
      const FeedbackRequest& request) const;

 private:
  std::shared_ptr<AmsClient> client_;
};

}  // namespace amssdk

#endif  // AMSSDK_CHAT_MODULE_H
