#ifndef AMSSDK_WORKFLOW_MODULE_H
#define AMSSDK_WORKFLOW_MODULE_H

#include "../core/ams_client.h"
#include "../include/workflow.h"
#include "../include/common/api_result.h"

namespace amssdk {

class WorkflowModule {
 public:
  using StreamEventCallback = std::function<void(std::unique_ptr<StreamEvent>)>;

  LIBAMS_EXPORT explicit WorkflowModule(std::shared_ptr<AmsClient> client);
  LIBAMS_EXPORT ~WorkflowModule() = default;

  // 工作流相关API
  LIBAMS_EXPORT ApiResult<WorkflowRunResponse> WorkflowRun(
      const WorkflowRunRequest& request,
      const StreamEventCallback& stream_event_callback) const;
  
  LIBAMS_EXPORT ApiResult<WorkflowRunInfoResponse> WorkflowRunInfo(
      const WorkflowRunInfoRequest& request) const;
  
  LIBAMS_EXPORT ApiResult<SimpleResponse> WorkflowTaskStop(
      const WorkflowTaskStopRequest& request) const;
  
  LIBAMS_EXPORT ApiResult<WorkflowLogsResponse> WorkflowLogs(
      const WorkflowLogsRequest& request) const;

 private:
  std::shared_ptr<AmsClient> client_;
};

}  // namespace amssdk

#endif  // AMSSDK_WORKFLOW_MODULE_H
