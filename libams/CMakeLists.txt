cmake_minimum_required(VERSION 4.0)
project(libams)

set(CMAKE_CXX_STANDARD 14)

find_package(curl REQUIRED)
find_package(nlohmann_json CONFIG REQUIRED)

include_directories(${CMAKE_CURRENT_SOURCE_DIR})

add_library(libams SHARED
        network/authorization.cpp
        network/authorization.h
        network/exception.h
        network/netimpl.cpp
        network/netimpl.h
        network/network.h
        network/response.cpp
        network/response.h
        network/http_client.cpp
        network/http_client.h


        serializer/serializer.cpp
        serializer/serializer.h
        include/chat/chat_request.h
        include/chat/chat_stream_event.h
        serializer/serializer_utils.cc
        serializer/serializer_utils.h
        serializer/deserializer.cpp
        serializer/deserializer.h
        serializer/stream_event_deserializer.cpp
        serializer/stream_event_deserializer.h

        api/api_manager.cpp
        api/api_manager.h
        api/services/chat_service.cpp
        api/services/chat_service.h
        api/services/task_service.cpp
        api/services/task_service.h
        api/services/file_service.cpp
        api/services/file_service.h

        ams_client.cpp
        ams_client.h

        include/file.h
        include/common/common.h
        include/task.h
        include/chat/chat_response.h
        api/services/conversation_service.cpp
        api/services/conversation_service.h
        include/conversation/conversation_request.h
        include/conversation/conversation_response.h
        include/audio.h
        api/services/audio_service.cpp
        api/services/audio_service.h
        include/app.h
        api/services/app_service.cpp
        api/services/app_service.h
        include/workflow.h
        api/services/workflow_service.cpp
        api/services/workflow_service.h
        include/knowledge/knowledge.h
        include/knowledge/knowledge_segments.h
        api/services/knowledge_service.cpp
        api/services/knowledge_service.h
        api/business/agent.cpp
        api/business/agent.h

)

target_link_libraries(libams PUBLIC nlohmann_json::nlohmann_json CURL::libcurl)
target_include_directories(${PROJECT_NAME}
        PUBLIC
        "$<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}>"
)